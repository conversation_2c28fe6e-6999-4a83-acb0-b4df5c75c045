<template>
  <div :id="id" class="map-container" ref="mapChartRef" />
</template>

<script setup>
import { setPx } from '@/utils/height'
import * as echarts from 'echarts'
import axios from 'axios'

const CHINA_CODE = '1000000'
const chinaMap = ref({})
const scatterData = ref([])
const mapJson = ref({})
const mapChart = ref(null)
const mapChartRef = ref(null)
const map = ref('china')
const currentCode = ref(CHINA_CODE)
const emit = defineEmits('clickMap')
const props = defineProps({
  heatMapData: {
    type: Array,
    default: () => [],
  },
  areaCode: {
    type: Object,
    default: {
      code: '500000',
      level: 1,
    },
  },
  aspectScale: {
    type: Number,
    default: 0.8,
  },
  isClick: {
    type: Boolean,
    default: false,
  },
  tooltip: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: 'mapChart',
  },
})
const getMapData = function (region) {
  console.log('ChainMap getMapData:', region)
  if (region.code === CHINA_CODE) {
    map.value = 'china'
  } else map.value = region.name
  const isLevel3 = region.level === '3'
  currentCode.value = region.code
  if (isLevel3) {
    // 如果是区县级，使用父级代码（市级）
    currentCode.value = region.parent || region.code.substring(0, 4) + '00'
  }
  axios
    .get(`https://feifeife.oss-cn-beijing.aliyuncs.com/opensource/map_json_1.0/json/${currentCode.value}.json`)
    .then(res => {
      if (!res) return
      mapJson.value = res.data
      if (isLevel3) {
        mapJson.value = {
          type: res.data.type,
          features: res.data.features.filter(item => region.code.includes(String(item.properties.adcode))),
        }
      }
      if (currentCode.value === CHINA_CODE) {
        chinaMap.value = res.data
      }
      registerMap(currentCode.value === CHINA_CODE ? 'china' : 'custom', mapJson.value)
      initMap({ geo: { map: currentCode.value === CHINA_CODE ? 'china' : 'custom' } })
    })
    .catch(err => {
      // 如果错误了就用上一个
    })
}
const getPeripheryMap = function (names) {
  const china = chinaMap.value
  const peripheryMap = {
    type: china.type,
    features: china.features.filter(feature => {
      return names.some(item => feature.properties.name.includes(item))
    }),
  }

  return peripheryMap
}
const registerMap = function (name, map) {
  echarts.registerMap(name, map)
}

const getGeo = function (options = {}) {
  return {
    type: 'map',
    map: currentCode.value === CHINA_CODE ? 'china' : 'custom',
    roam: false, // 缩放 | 移动
    aspectScale: props.aspectScale, // 长宽比
    label: {
      show: false,
      emphasis: {
        show: false,
        textStyle: {
          color: '#fff',
        },
      },
    },
    itemStyle: {
      normal: {
        areaColor: '#fff',
        borderColor: '#389dff',
        borderWidth: 0.5,
      },
      emphasis: {
        areaColor: '#2e317c',
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        borderWidth: 0,
      },
    },
    ...options,
  }
}
const getVisualMap = function (data, options) {
  const min = data[0]?.value || 0
  const max = data[data.length - 1]?.value || 0
  return {
    show: true,
    left: 0,
    bottom: 0,
    max,
    min,
    calculable: false,
    itemWidth: setPx(32),
    itemHeight: setPx(140),
    align: 'right',
    textGap: setPx(8),
    text: ['高', '低'], // 文本，默认为数值文本
    inRange: {
      color: props.heatMapData.length > 1 ? ['#ECF8FF', '#388BE9'] : ['#ECF8FF'],
      symbolSize: [10, 100],
    },
    textStyle: {
      color: '#282D30',
    },
    seriesIndex: 0,
    ...options,
  }
}
const initMap = function (options = {}) {
  if (mapChart.value) {
    mapChart.value.dispose()
    mapChart.value = null
  }

  mapChart.value = echarts.init(document.querySelector(`#${props.id}`))

  const option = {
    grid: {
      left: setPx(40),
      top: setPx(50),
      right: setPx(52),
      bottom: setPx(30),
      containLabel: true,
    },
    tooltip: {
      show: true,
      // formatter: params => {
      //     return `${params.name} ${isNaN(params.value) ? 0 : params.value}`
      // },
      formatter: params => {
        if (props.tooltip) {
          return `
                  <div style="min-width: 96px;padding-left: 5px">
                    <div style="font-weight:600">${params.name}</div>
                    <span style="color: #E72410; margin-right: 8px;font-weight:600">${
                      isNaN(params.value) ? 0 : params.value
                    }</span><span style="color:#74798C">家</span>
                  </div>
                `
        } else {
          return `${params.name} ${isNaN(params.value) ? 0 : params.value}`
        }
      },
      textStyle: {
        fontSize: setPx(24),
      },
    },
    geo: [getGeo(options.geo)],
    visualMap: [],
    series: [],
  }
  const json = mapJson.value.features.map(item => {
    return {
      ...item,
      ...item.properties,
    }
  })

  // 添加热力图
  const data = mergeArray(props.heatMapData, json).sort((a, b) => a.value - b.value)
  const seriesIndex = option.series.length
  option.visualMap.push(getVisualMap(data, { seriesIndex }))

  option.series[seriesIndex] = {
    name: '',
    type: 'map',
    data: data,
    geoIndex: 0,
    selectedMode: false,
  }

  // window.onresize = function () {
  //   //自适应大小
  //   mapChart.value.resize()
  // }
  // 点击事件
  mapChart.value.on('click', e => {
    if (!props.isClick) return
    nextTick(() => {
      emit('clickMap', e)
    })
  })
  renderChart(option)
}
const renderChart = function (option) {
  mapChart.value && mapChart.value.setOption(option)
  nextTick(() => {
    setTimeout(() => {
      const container = mapChartRef.value
      const canvas = container?.querySelector('canvas')
      canvas.click()
    }, 300)
  })
}
const mergeArray = function (ary1, ary2) {
  const result = []
  for (let i = 0; i < ary2.length; i++) {
    const item1 = ary2[i]
    const find = ary1.find(item => item.name === item1.name)
    const res = find || { name: item1.name, value: 0 }
    result.push(Object.assign(item1, res))
  }
  return result
}
watch(
  () => props.heatMapData,
  () => {
    if (mapChart.value) {
      initMap()
    }
  },
  {
    deep: true,
    immediate: true,
  }
)
watch(
  () => props.areaCode,
  val => {
    // console.log('val', val)
    getMapData(val)
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 100%;
}
</style>
