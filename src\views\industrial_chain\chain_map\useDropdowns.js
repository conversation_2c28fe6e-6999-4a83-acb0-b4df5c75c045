import { reactive, computed } from 'vue'

/**
 * 下拉框管理 Hook
 * 提取下拉框的交互逻辑，分离业务逻辑和UI逻辑
 */
export function useDropdowns(onDataChange) {
  const data = reactive({
    chainDropdownVisible: false,
    regionDropdownVisible: false,
    selectedChainData: [],
    selectedRegionData: [],
    regionMode: 'nearby', // 'nearby' | 'national'
  })

  // 计算属性
  const selectedChain = computed(() => {
    return data.selectedChainData[0] || { name: '请选择产业链' }
  })

  const selectedRegion = computed(() => {
    return data.selectedRegionData[0] || { name: '全国', code: 'All', level: '0' }
  })

  // 产业链下拉框相关方法
  const toggleChainDropdown = () => {
    data.chainDropdownVisible = !data.chainDropdownVisible
    data.regionDropdownVisible = false // 关闭地区下拉框
  }

  const handleChainDropdownClose = () => {
    data.chainDropdownVisible = false
  }

  const handleChainSubmit = result => {
    // ChainSelection 返回的数据格式: { selection: {...}, path: {...} }
    if (result && result.selection) {
      data.selectedChainData = [result.selection]
    }
    data.chainDropdownVisible = false

    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'chain',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 地区模式切换
  const switchRegionMode = mode => {
    // 如果点击的是当前已选中的模式，且是全国模式，则打开下拉框
    if (data.regionMode === mode && mode === 'national') {
      data.regionDropdownVisible = !data.regionDropdownVisible
      data.chainDropdownVisible = false // 关闭产业链下拉框
      return
    }

    const previousMode = data.regionMode
    data.regionMode = mode
    data.regionDropdownVisible = false // 关闭下拉框
    data.chainDropdownVisible = false // 关闭产业链下拉框
    // 处理地区数据的切换逻辑
    if (mode === 'nearby') {
      // 切换到周边模式
      if (previousMode === 'national') {
        // 从全国模式切换到周边模式
        if (data.selectedRegionData.length > 0 && data.selectedRegionData[0].code === 'All') {
          // 如果之前选择的是全国，保持全国选择
          // 周边模式下的全国，地图显示全国，数据也是全国
          // 不需要改变 selectedRegionData
        }
        // 如果之前选择的是具体地区，保持该地区选择
        // 周边模式下，地图会显示上级地区，但数据仍用当前地区
      }
    } else if (mode === 'national') {
      // 切换到全国模式
      if (previousMode === 'nearby') {
        // 从周边模式切换到全国模式
        if (data.selectedRegionData.length === 0 || data.selectedRegionData[0].code === 'All') {
          // 如果之前没有选择或选择的是全国，设置为全国
          data.selectedRegionData = [
            {
              code: 'All',
              name: '全国',
              level: '0',
            },
          ]
        }
        // 如果之前选择的是具体地区，保持该地区选择
        // 全国模式下，地图显示该地区，数据也用该地区
      } else {
        // 初始化或其他情况，设置默认为全国
        data.selectedRegionData = [
          {
            code: 'All',
            name: '全国',
            level: '0',
          },
        ]
      }
    }

    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'regionMode',
        mode: mode,
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 地区下拉框相关方法
  const toggleRegionDropdown = () => {
    // 只有在全国模式下才能打开下拉框
    if (data.regionMode === 'national') {
      data.regionDropdownVisible = !data.regionDropdownVisible
      data.chainDropdownVisible = false // 关闭产业链下拉框
    }
  }

  const handleRegionDropdownClose = () => {
    data.regionDropdownVisible = false
  }

  const handleRegionSubmit = result => {
    console.log(33333333333333, result)

    // RegionSelection 返回的数据格式: { selection: {...}, path: {...} }
    if (result && result.selection) {
      data.selectedRegionData = [result.selection]
    }
    data.regionDropdownVisible = false

    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'region',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 初始化数据
  const initializeData = routeQuery => {
    const { code, name } = routeQuery
    if (code && name) {
      // 设置初始选中的产业链数据
      data.selectedChainData = [
        {
          chain_code: code,
          name: name,
        },
      ]
    }

    // 默认选择周边模式
    data.regionMode = 'nearby'
    data.selectedRegionData = []

    // 通知初始数据
    if (onDataChange) {
      onDataChange({
        type: 'init',
        mode: data.regionMode,
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 获取当前选择的数据
  const getCurrentSelection = () => {
    return {
      chainData: data.selectedChainData,
      regionData: data.selectedRegionData,
    }
  }

  return {
    // 响应式数据
    data,
    selectedChain,
    selectedRegion,

    // 产业链下拉框方法
    toggleChainDropdown,
    handleChainDropdownClose,
    handleChainSubmit,

    // 地区下拉框方法
    switchRegionMode,
    toggleRegionDropdown,
    handleRegionDropdownClose,
    handleRegionSubmit,

    // 工具方法
    initializeData,
    getCurrentSelection,
  }
}
