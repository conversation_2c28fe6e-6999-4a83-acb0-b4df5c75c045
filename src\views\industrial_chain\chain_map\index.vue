<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 左边产业链下拉框 -->
      <div class="dropdown-container">
        <div
          class="dropdown-trigger"
          @click="toggleChainDropdown"
          :class="{ 'dropdown-actives': data.chainDropdownVisible }"
        >
          <span :class="[data.chainDropdownVisible ? 'text-[#07a6f0]' : '']">{{
            selectedChain.name || '请选择产业链'
          }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20 text-[#A0A5BA]"
            :class="[data.chainDropdownVisible ? 'rotate-180 text-[#07a6f0]' : '']"
          />
        </div>
      </div>
      <!-- 右边地区下拉框 -->
      <div class="btn_group">
        <div class="region-tab" :class="{ active: data.regionMode === 'nearby' }" @click="switchRegionMode('nearby')">
          周边
        </div>
        <div
          class="region-tab f-all-center"
          :class="{ active: data.regionMode === 'national' }"
          @click="switchRegionMode('national')"
        >
          <span>{{ selectedRegion.name || '全国' }}</span>
          <svg-icon
            v-if="data.regionMode === 'national'"
            name="arrow_d_w"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.regionDropdownVisible ? 'rotate-180' : '']"
            @click.stop="toggleRegionDropdown"
          />
        </div>
      </div>
    </div>
    <!-- 下面超出就局部滚动 -->
    <div>
      <!-- 地图 -->
      <div class="w-full h-534">
        <ChainMap :heatMapData="mapData.heatMapData" :areaCode="mapData.areaCode" :isClick="false" :tooltip="true" />
      </div>
      <!-- 推荐企业名单取前十个 -->
      <div v-if="entListData.list.length > 0" class="px-24 py-16">
        <div class="text-32 font-semibold text-[#20263A] mb-16">推荐企业（前{{ entListData.list.length }}家）</div>
        <EntCard :sourceData="entListData.list" type="noThreeAddCollect" :showContacts="false" />
      </div>
      <!-- 空状态 -->
      <div v-else-if="!entListData.loading" class="px-24 py-32 text-center">
        <div class="text-28 text-[#999]">暂无企业数据</div>
      </div>
    </div>

    <!-- 产业链下拉框 -->
    <ChainSelection
      :visible="data.chainDropdownVisible"
      :defaultCode="data.selectedChainData.length > 0 ? data.selectedChainData[0].chain_code : ''"
      :headHeight="headHeight"
      @close="handleChainDropdownClose"
      @submit="handleChainSubmit"
    />

    <!-- 地区下拉框 -->
    <RegionSelection
      :visible="data.regionDropdownVisible"
      :defaultCode="data.selectedRegionData.length > 0 ? data.selectedRegionData[0].code : 'All'"
      :headHeight="headHeight"
      @close="handleRegionDropdownClose"
      @submit="handleRegionSubmit"
    />
  </div>
</template>

<script setup>
import ChainMap from './ChainMap.vue'
import ChainSelection from './ChainSelection.vue'
import RegionSelection from './RegionSelection.vue'
import EntCard from '@/components/EntCard/index.vue'
import { hotCount, industryListTen } from '@/api/iChain/index'
import { useDropdowns } from './useDropdowns.js'

const route = useRoute()
const headRef = ref(null)

// 响应式数据
const mapData = reactive({
  heatMapData: [], // 热力图数据
  areaCode: { code: '1000000', level: 1 }, // 地图区域代码
})

const entListData = reactive({
  loading: false,
  list: [], // 企业列表
  total: 0,
})

const headHeight = computed(() => {
  return headRef.value?.offsetHeight || 88
})

// 获取地区的上级区域代码
const getParentAreaCode = (areaCode, level) => {
  if (!areaCode || areaCode === 'All') return '1000000' // 全国

  const codeStr = String(areaCode)

  // 如果是直辖市（北京、天津、上海、重庆）
  const municipalities = ['110000', '120000', '310000', '500000']
  if (municipalities.includes(codeStr)) {
    return '1000000' // 直辖市的上级是全国
  }

  // 根据当前级别获取上级
  if (level === '3') {
    // 区县级 -> 市级
    return codeStr.substring(0, 4) + '00'
  } else if (level === '2') {
    // 市级 -> 省级
    return codeStr.substring(0, 2) + '0000'
  } else if (level === '1') {
    // 省级 -> 全国
    return '1000000'
  }

  return '1000000' // 默认全国
}

// 根据地区级别获取 agg_type
const getAggType = (level, areaCode) => {
  if (!level || level === '0') return 0 // 全国

  const codeStr = String(areaCode || '')
  // 直辖市特殊处理
  const municipalities = ['110000', '120000', '310000', '500000']
  if (municipalities.includes(codeStr)) {
    return 6 // 直辖市用6
  }

  switch (level) {
    case '1':
      return 4 // 省级
    case '2':
      return 6 // 市级
    case '3':
      return 6 // 区县级
    default:
      return 0 // 全国
  }
}

// 获取地区名称
const getAreaName = (areaCode, level) => {
  if (!areaCode || areaCode === '1000000') return '全国'
  return areaCode
}

// 构建请求参数和地图显示参数
const buildParams = (mode, selectedRegion, chainCodes) => {
  // 确保 selectedRegion 有默认值
  const region = selectedRegion || { code: 'All', level: '0', name: '全国' }

  // 基础请求参数
  const requestParams = {
    areas: [],
    chain_codes: chainCodes,
    agg_type: 0,
  }

  // 基础地图显示参数
  let mapDisplayParams = {
    code: '1000000', // 默认全国
    level: '0',
    name: '全国',
  }

  // 根据模式和选中地区构建参数
  if (!region.code || region.code === 'All') {
    // 选择全国或没有选择地区
    requestParams.areas = []
    requestParams.agg_type = 0
    mapDisplayParams = { code: '1000000', level: '0', name: '全国' }
  } else {
    // 选择了具体地区
    if (mode === 'national') {
      // 全国模式：地图显示选中地区，数据也用选中地区
      requestParams.areas = [region.code]
      requestParams.agg_type = getAggType(region.level, region.code)
      mapDisplayParams = {
        code: region.code,
        level: region.level,
        name: region.name,
      }
    } else {
      // 周边模式：地图显示上级地区，数据用选中地区
      requestParams.areas = [region.code]
      requestParams.agg_type = getAggType(region.level, region.code)

      // 地图显示上级地区
      const parentCode = getParentAreaCode(region.code, region.level)
      const parentLevel = Math.max(0, parseInt(region.level || '0') - 1).toString()
      mapDisplayParams = {
        code: parentCode,
        level: parentLevel,
        name: getAreaName(parentCode, parentLevel),
      }
    }
  }

  return { requestParams, mapDisplayParams }
}

// 业务逻辑：获取地图热力数据
const getMapHot = async selectionData => {
  console.log('地图数据更新:', selectionData)

  // 确保有模式信息和默认值
  const mode = selectionData.mode || 'nearby'
  const selectedRegion = selectionData.regionData?.[0] || { code: 'All', level: '0', name: '全国' }
  const chainCodes = selectionData.chainData?.map(item => item.chain_code) || []

  // 构建数据请求参数和地图显示参数
  const { requestParams, mapDisplayParams } = buildParams(mode, selectedRegion, chainCodes)

  console.log('请求参数:', requestParams)
  console.log('地图显示参数:', mapDisplayParams)

  try {
    // 调用热力图接口
    if (requestParams.chain_codes.length > 0) {
      console.log('正在请求热力图数据，参数:', requestParams)

      try {
        const response = await hotCount(requestParams)
        if (response && response.data && response.data.datalist) {
          // 转换数据格式为地图组件需要的格式
          mapData.heatMapData = response.data.datalist.map(item => ({
            name: item.region_name,
            value: item.count,
            region_code: item.region_code,
            region_full_title: item.region_full_title,
          }))
          console.log('热力图数据获取成功:', mapData.heatMapData.length, '条')
        } else {
          console.log('热力图接口返回数据为空')
          mapData.heatMapData = []
        }
      } catch (apiError) {
        console.error('热力图API请求失败:', apiError)
        mapData.heatMapData = []
      }
    } else {
      console.log('没有产业链代码，清空热力图数据')
      mapData.heatMapData = []
    }

    // 更新地图区域代码（地图显示的区域）
    const newAreaCode = {
      code: mapDisplayParams.code,
      level: mapDisplayParams.level,
      name: mapDisplayParams.name,
    }

    console.log('更新地图区域代码:', {
      old: mapData.areaCode,
      new: newAreaCode,
      mode,
      selectedRegion,
    })

    mapData.areaCode = newAreaCode

    console.log('地图参数:', {
      requestParams,
      mapAreaCode: mapData.areaCode,
      mode,
    })
  } catch (error) {
    console.error('获取地图热力数据失败:', error)
    mapData.heatMapData = []
  }

  // 同时获取企业列表数据
  await getEntList(selectionData, requestParams)
}

// 获取企业列表数据
const getEntList = async (selectionData, hotCountParams) => {
  // 构建请求参数 - 基于热力图的参数，但添加企业列表特有的参数
  const params = {
    ...hotCountParams, // 复用热力图的参数（areas, chain_codes, agg_type）
    insight: true,
    level: 2,
    page_index: 1,
    page_size: 10,
    sort: { name: 'REGCAP', order: 'DESC' },
  }

  try {
    entListData.loading = true
    if (params.chain_codes.length > 0) {
      console.log('正在请求企业列表数据，参数:', params)
      const response = await industryListTen(params)
      if (response && response.data) {
        entListData.list = response.data.items || []
        entListData.total = response.data.count || 0
        console.log('企业列表数据获取成功:', entListData.list.length, '条')
      } else {
        console.log('企业列表接口返回数据为空')
        entListData.list = []
        entListData.total = 0
      }
    } else {
      console.log('没有产业链代码，清空企业列表')
      entListData.list = []
      entListData.total = 0
    }
  } catch (error) {
    console.error('获取企业列表失败:', error)
    entListData.list = []
    entListData.total = 0
  } finally {
    entListData.loading = false
  }
}

// 使用下拉框 hook
const {
  data,
  selectedChain,
  selectedRegion,
  toggleChainDropdown,
  handleChainDropdownClose,
  handleChainSubmit,
  switchRegionMode,
  toggleRegionDropdown,
  handleRegionDropdownClose,
  handleRegionSubmit,
  initializeData,
} = useDropdowns(getMapHot)

onMounted(() => {
  // 初始化下拉框数据
  initializeData(route.query)
})
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
      .svg-left {
        color: red !important;
      }
    }
  }
  .btn_group {
    @apply flex;
    height: 56px;
    background: #f4f4f4;
    border-radius: 8px;
    overflow: hidden;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    .region-tab {
      min-width: 124px;
      height: 56px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        color: #fff !important;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        span {
          color: #fff !important;
        }
      }

      &:hover:not(.active) {
        background: #e8e8e8;
      }
    }
  }
}
</style>
